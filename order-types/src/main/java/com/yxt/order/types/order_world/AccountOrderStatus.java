package com.yxt.order.types.order_world;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AccountOrderStatus {
  //WAIT-待下账 PROCESS-下账中 SUCCESS-下账成功 FAIL-下账失败
  WAIT( "WAIT","待下账"),
  PROCESS("PROCESS","下账中"),
  SUCCESS("SUCCESS","下账成功"),
  FAIL( "FAIL","下账失败"),
  CANCEL("CANCEL","下账取消"),
  ;

  private final String status;
  private final String msg;

  //根据状态获取枚举
  public static AccountOrderStatus getByStatus(String status) {
    for (AccountOrderStatus value : values()) {
      if (value.status.equals(status)) {
        return value;
      }
    }
    return null;
  }

}
