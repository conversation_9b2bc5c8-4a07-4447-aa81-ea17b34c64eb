package com.yxt.order.types.order_world;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * POS下账枚举
 * <AUTHOR>
 * @date 2024/1/4
 * @since 1.0
 */
@Getter
@AllArgsConstructor
public enum AccountOrderPosModeEnum {
    POS_HD_H1("HD_H1","海典H1"),
    POS_HD_H2("HD_H2","海典H2"),
    POS_KC("KC","科传"),
    WMS("WMS","WMS发货并下账");
    private final String code;
    private final String name;


    public static AccountOrderPosModeEnum getPosMode(String code) {
        for (AccountOrderPosModeEnum posModeEnum : AccountOrderPosModeEnum.values()) {
            if (posModeEnum.getCode().equalsIgnoreCase(code)) {
                return posModeEnum;
            }
        }
        return null;
    }
}
