package com.yxt.order.types.utils;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.io.BufferedReader;
import java.io.FileReader;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class OrderRepair {

  public static void main(String[] args) {

    Set<String> failedOrderSet = new HashSet<>();
    String filePath = "C:\\Users\\<USER>\\Desktop\\yn_order.txt"; // 替换为你的文件路径
    int maxLines = 21000; // 要读取的最大行数
    int currentLine = 0;

    Map<String,Object> request = new HashMap<>();
    request.put("merCode","500001");
    request.put("platformCode","27");
    request.put("onlineClientCode","3d5c238ad8f841c6ba2d5bfe49d395dd");

    String orderNo = null;
    try (BufferedReader br = new BufferedReader(new FileReader(filePath))) {
      while ((orderNo = br.readLine()) != null && currentLine < maxLines) {
        // 处理每一行数据
        HashMap<String, Object> body = new HashMap<>();
        body.put("thirdOrderNo",orderNo);
        request.put("body",body);
        //请求url
        String result = HttpUtil.post("third-platform-order-mt.svc.k8s.pro.hxyxt.com/third-platform/order/27/repair", JSON.toJSONString(request));
        if (result != null) {
          JSONObject response = JSON.parseObject(result);
          String responseCode = response.getString("code");
          if(!responseCode.equals("10000")){
            failedOrderSet.add(orderNo);
          }
        }
        currentLine++;
        ThreadUtil.safeSleep(100L);
      }
    } catch (Exception e) {
      failedOrderSet.add(orderNo);
    }
    System.out.println(JSON.toJSONString(failedOrderSet));
  }

}
