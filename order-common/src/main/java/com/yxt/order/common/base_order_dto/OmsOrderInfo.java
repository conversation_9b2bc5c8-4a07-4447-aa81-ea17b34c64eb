package com.yxt.order.common.base_order_dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 系统子订单信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OmsOrderInfo implements Serializable {

  private static final long serialVersionUID = 1L;

  @JsonSerialize(using = ToStringSerializer.class)
  private Long id;

  /**
   * 订单号
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long orderNo;

  /**
   * 子订单号
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long omsOrderNo;


  /**
   * 发货单号
   */
  @JsonSerialize(using = ToStringSerializer.class)
  private Long omsShipNo;

  /**
   * 订单状态:10待审核,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭, (特别注意 5是处方单)
   */
  private Integer orderStatus;

  /**
   * 仓库id
   */
  private String warehouseId;


  private String warehouseCode;
  /**
   * 仓库名称
   */
  private String warehouseName;

  /**
   * 快递名称
   */
  private String expressName;

  /**
   * 快递id
   */
  private Integer expressId;

  /**
   * 快递单号
   */
  private String expressNumber;

  /**
   * 系统备注
   */
  private String remark;

  /**
   * 创建时间
   */
  private Date createTime;

  /**
   * 创建人
   */
  private String creator;

  /**
   * 修改时间
   */
  private Date modifyTime;

  /**
   * 订单类型，1 平台订单（常规的）、2 导入订单（导）、3 手工订单（手）、4 拆分订单（拆）
   */
  private Integer orderType;

  /**
   * 订单拆分状态：0 未拆单，1 拆分订单的源头订单，2 拆分的最终订单，3 拆分中间状态
   */
  private Integer splitStatus;

  /**
   * 审核时间
   */
  private LocalDateTime auditTime;

  /**
   * 发货时间
   */
  private LocalDateTime shipTime;

  /**
   * 是否有退款标记，0 无,1 有
   */
  private Boolean isRefund;

  /**
   * 发货状态 10 待拣货, 20 待生成面单, 25 待打印面单, 30 待发货, 40 已发货
   */
  private Integer shipStatus;

  /**
   * 发货人员id
   */
  private String shipOperatorId;

  /**
   * 发货人姓名
   */
  private String shipOperatorName;

  /**
   * 审核人员id
   */
  private String auditOperatorId;

  /**
   * 审核人员姓名
   */
  private String auditOperatorName;

  /**
   * 异常处理人id
   */
  private String exOperatorId;

  /**
   * 异常处理人姓名
   */
  private String exOperatorName;

  /**
   * 下账处理人id
   */
  private String billOperatorId;

  /**
   * 下账处理人姓名
   */
  private String billOperatorName;

  /**
   * 下账时间
   */
  private LocalDateTime billTime;

  /**
   * 异常处理时间
   */
  private LocalDateTime exOperatorTime;

  /**
   * 完成时间
   */
  private LocalDateTime completeTime;

  /**
   * 取消时间
   */
  private LocalDateTime cancelTime;

  /**
   * 订单拦截状态: -1.拦截失败 0.未拦截 1.拦截成功
   */
  private Integer interceptStatus;

  /**
   * 异常状态:  0.无异常 1.异常 1,疑似刷单订单 2，订单金额异常 3，商品库存不足 4，商品数量异常 5，商品不存在 6，订单预估毛利异常 7，仓库发货失败 8，平台发货失败 11.
   * 修改地址失败
   */
  private Integer exStatus;

  @ApiModelProperty("ERP审核错误码")
  private String erpErrorCode;

  /**
   * erp下账状态: 20.待退账（待锁定）,30.待下账，100.已退账（已下账）,102.退款已取消，110.已取消，120.已退款
   */
  private Integer erpStatus;

  /**
   * erp配送单
   */
  private String erpDeliverNo;

  /**
   * ERP零售单号
   */
  private String erpSaleNo;
  /**
   * 异常原因
   */
  private String exReason;

  /**
   * 是否为邮费单 0否，1是
   */
  private Integer isPostFeeOrder;

  /**
   * 商户是否对接wms：0-ERP不对接WMS ；为空或者1时-ERP对接WMS
   */
  private Integer joinWms;

  /**
   * 下单真实时间
   */
  private LocalDateTime created;

  /**
   * 线上门店编码
   */
  private String onlineStoreCode;

  private Date payTime;


  /**
   * 平台编码
   */
  private String thirdPlatformCode;
  /**
   * 第三方平台订单号
   */
  private String thirdOrderNo;
  /**
   * 商户编码
   */
  private String merCode;
  /**
   * 网店编码
   */
  private String clientCode;

  /**
   * 下单线上门店名称
   */
  private String onlineStoreName;
  /**
   * 线下门店编码
   */
  private String organizationCode;
  /**
   * 线下门店名称
   */
  private String organizationName;
  /**
   * 是否是审方单（结合审方配置）
   */
  private Integer isPrescription;

  /**
   * 三方平台买家昵称
   */
  private String buyerName;

  /**
   * 网店配置ID
   */
  private Long clientConfId;

  /**
   * 是否开票，1.开发票；2.不开发票
   */
  private String needInvoice;

  /**
   * 买家留言
   */
  private String buyerMessage;

  /**
   * 卖家备注
   */
  private String sellerRemark;

  /**
   * 仓库类型0-不是仓库，1-普通仓库，2-门店仓库，3-供货商仓库
   */
  private Integer warehouseType;

  /**
   * 供应商编码 无供应商的默认填0,便于查询
   */
  private String supplierCode;

  /**
   * 快递面单状态：0-未生成，1-未打印，2-已打印
   */
  private String sheetStatus;
  /**
   * o2o的网店编码
   */
  private String o2oClientCode;
  /**
   * o2o门店机构编码
   */
  private String o2oShopId;


  /**
   * 发货单打印次数
   */
  private Integer sendOrderPrintNum;

  @ApiModelProperty("订单商品总数量")
  private Integer goodsQty;

  @ApiModelProperty("订单商品种类数")
  private Integer goodsCategoryQty;


  @ApiModelProperty("买家id")
  private String buyerId;

  @ApiModelProperty("订单标记")
  private OrderTag tag;

  @ApiModelProperty("订单序号按商户按天生成")
  private Long seq;

  @ApiModelProperty("物流是否已回传平台状态")
  private Integer shippedStatus;

  @ApiModelProperty("推广门店")
  private String spreadStoreCode;

  @ApiModelProperty("订单归属类型 0-为商户，默认；1-为供应商订单")
  private Integer orderOwnerType;

  @ApiModelProperty("微商城会员编号")
  private String memberNo;

  @ApiModelProperty(value = "备货状态 2-已备货 1-待备货", notes = "云仓订单专用")
  private String stockState;
  @ApiModelProperty("ERP审核中")
  private Integer erpAuditStatus;

  @ApiModelProperty("订单结算状态：0-待结算，1-已结算，9-已失效")
  private Integer settlementStatus;

  @ApiModelProperty("扩展字段")
  private String extendInfo;

  @ApiModelProperty("逻辑删除字段 默认0-未删除")
  private Long deleted;

  /**
   * 请货单关联字段
   */
  @ApiModelProperty("请货单关联字段")
  private Long procurementNo;

  /**
   * D-ERP请货状态：0-未请货 1已请货
   */
  @ApiModelProperty("D-ERP请货状态：0-未请货 1已请货")
  private Integer isProcurementErp;

  /**
   * 平台
   */
  @ApiModelProperty("下单平台")
  private String platform;

  /**
   * 下单渠道
   */
  @ApiModelProperty("下单渠道")
  private String subBizType;

  /**
   * 用户等级
   */
  @ApiModelProperty("用户等级")
  private String vipLevel;

  /**
   * 已回传物流信息，0 未回传,1 已回传
   */
  @ApiModelProperty("已回传物流信息，0 未回传,1 已回传")
  private Integer logisticsBackStatus;

}
