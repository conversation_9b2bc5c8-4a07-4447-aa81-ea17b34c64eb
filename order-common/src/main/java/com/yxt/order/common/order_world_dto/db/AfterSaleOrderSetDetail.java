package com.yxt.order.common.order_world_dto.db;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 心云售后单明细
 */
@Data
public class AfterSaleOrderSetDetail {

  /**
   * 主键
   */
  private Long id;

  /**
   * 心云售后单号
   */
  private String afterSaleNo;

  /**
   * 心云售后单明细唯一号
   */
  private String afterSaleOrderDetailSetNo;

  /**
   * 平台编码
   */
  private String thirdPlatformCode;

  /**
   * 平台售后单号
   */
  private String thirdAfterOrderNo;

  /**
   * 平台售后单明细唯一号
   */
  private String thirdAfterOrderDetailNo;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品名称
   */
  private String erpName;

  /**
   * 申请商品数量
   */
  private BigDecimal planCommodityCount;

  /**
   * 实际商品数量
   */
  private BigDecimal realCommodityCount;

  /**
   * 明细状态: NORMAL-正常 exchange-换货  exchanged-换货后
   */
  private String status;

  /**
   * 仅换货有值.换货后的内部明细编号
   */
  private String swapNo;

  /**
   * 商品行号
   */
  private String rowNo;

  /**
   * 三方平台商品编码
   */
  private String platformSkuId;

  /**
   * 第三方详情ID
   */
  private String platformDetailId;

  /**
   * 订单明细ID
   */
  private String orderDetailNo;

  /**
   * 赠品类型 GIFT-赠品 NOT_GIFT - 非赠品
   */
  private String giftType;

  /**
   * 商品原单价
   */
  private BigDecimal originalPrice;

  /**
   * 商品售价
   */
  private BigDecimal price;

  /**
   * 商品成本价
   */
  private BigDecimal commodityCostPrice;

  /**
   * 商品总额=price*实际数量
   */
  private BigDecimal totalAmount;

  /**
   * 订单级别优惠分摊
   */
  private BigDecimal discountShare;

  /**
   * 明细级别折扣金额
   */
  private BigDecimal discountAmount;

  /**
   * 是否参加促销 TRUE、FALSE
   */
  private String isOnPromotion;

  /**
   * 是否拆零是买 true,false
   */
  private String detachable;

  /**
   * 平台创建时间
   */
  private LocalDateTime created;

  /**
   * 平台更新时间
   */
  private LocalDateTime updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}
