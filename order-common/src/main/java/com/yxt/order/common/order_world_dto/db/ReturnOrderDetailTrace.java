package com.yxt.order.common.order_world_dto.db;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 心云退货单明细追溯码信息
 */
@Data
public class ReturnOrderDetailTrace {

  /**
   * 主键
   */
  private Long id;

  /**
   * 退货单号
   */
  private String returnNo;

  /**
   * 退货单明细唯一号
   */
  private String returnDetailNo;

  /**
   * 退货单明细拣货唯一号
   */
  private String returnOrderDetailPickNo;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品批号
   */
  private String makeNo;

  /**
   * 追溯码
   */
  private String traceCode;

  /**
   * 医保上报标识
   */
  private String nhsaReportFlag;

  /**
   * 药监上报标识
   */
  private String draReportFlag;

  /**
   * 平台创建时间
   */
  private LocalDateTime created;

  /**
   * 平台更新时间
   */
  private LocalDateTime updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}
