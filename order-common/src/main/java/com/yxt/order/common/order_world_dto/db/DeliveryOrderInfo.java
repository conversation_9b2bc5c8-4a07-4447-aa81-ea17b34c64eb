package com.yxt.order.common.order_world_dto.db;

import com.yxt.order.common.order_world_dto.enums.ExtendDataTypeEnum;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import lombok.Data;

/**
 * 发货单主信息
 */
@Data
public class DeliveryOrderInfo {

  /**
   * 主键ID
   */
  private Long id;

  /**
   * 内部订单号，自己生成
   */
  private String orderNo;

  /**
   * 发货单号
   */
  private String deliveryOrderNo;

  /**
   * 平台发货单号
   */
  private String thirdDeliveryOrderNo;

  /**
   * 用户ID
   */
  private String userId;

  /**
   * 单据发起人
   */
  private String launchUserId;

  /**
   * 发货日期
   */
  private LocalDateTime deliveryDate;

  /**
   * 是否参加预约单 TRUE、FALSE
   */
  private String isOnBooking;

  /**
   * 仅预约单有值,预约送达开始时间
   */
  private LocalDateTime bookingTimeStart;

  /**
   * 仅预约单有值,预约送达结束时间
   */
  private LocalDateTime bookingTimeEnd;

  /**
   * 交易场景 online:代表线上交易 ,offline:代表线下交易
   */
  private String transactionChannel;

  /**
   * 业务类型 O2O、B2C、B2B
   */
  private String businessType;

  /**
   * 发起方所属机构编码,仅B2B场景有值
   */
  private String launchOrganizationCode;

  /**
   * 发起方所属机构名称,仅B2B场景有值
   */
  private String launchOrganizationName;

  /**
   * 计划发货方编码
   */
  private String senderPlanOrganizationCode;

  /**
   * 计划发货方名称
   */
  private String senderPlanOrganizationName;

  /**
   * 实际发货方编码
   */
  private String senderRealOrganizationCode;

  /**
   * 实际发货方名称
   */
  private String senderRealOrganizationName;

  /**
   * 发货单总金额，=累加发货明细 total_amount+delivery_plan_amount
   */
  private BigDecimal deliveryOrderTotalAmount;

  /**
   * 发货单预计运费
   */
  private BigDecimal deliveryPlanAmount;

  /**
   * 转单状态：
   * - 未转单
   * - 已转单
   */
  private String deliveryTransfer;

  /**
   * 发货状态：
   * - 待确认
   * - 待发货
   * - 运输中
   * - 已签收
   */
  private String deliveryStatus;

  /**
   * 发货地址
   */
  private String senderAddress;

  /**
   * 发货方联系电话
   */
  private String senderPhone;

  /**
   * 收货方编码
   */
  private String receiverCode;

  /**
   * 收货方名字
   */
  private String receiverName;

  /**
   * 收货方地址
   */
  private String receiverAddress;

  /**
   * 收货方电话
   */
  private String receiverPhone;

  /**
   * 收货人固定电话
   */
  private String receiverLandlinePhone;

  /**
   * 收货人平台OA唯一号
   */
  private String receiverThirdOaid;

  /**
   * 收货人地址信息-省份
   */
  private String receiverProvince; // 省份
  /**
   * 收货人地址信息-城市
   */
  private String receiverCity; // 城市
  /**
   * 收货人地址信息-区
   */
  private String receiverDistrict; // 区
  /**
   * 收货人地址信息-城镇
   */
  private String receiverTown; // 城镇
  /**
   * 收货人地址信息-详细地址
   */
  private String receiverFullAddress; //完整详细地址

  /**
   * 收货人地址信息-详细地址
   */
  private String receiverOriginalFullAddress; // 原始地址

  /**
   * 收货人地址信息-邮编
   */
  private String receiverZipCode; //邮编
  /**
   * 收货人地址信息-隐私地址
   */
  private String receiverNamePrivacy; //隐私收货人姓名
  /**
   * 收货人地址信息-隐私地址
   */
  private String receiverAddressPrivacy; //隐私收货人详细地址
  /**
   * 收货人地址信息-隐私地址
   */
  private String receiverPhonePrivacy; // 隐私收货人电话号码
  /**
   * 收货人地址信息-隐私地址
   */
  private String receiverLandlinePhonePrivacy; // 隐私收货人固定电话


  /**
   * 备注
   */
  private String remarks;

  /**
   * 商户编码
   */
  private String merCode;

  /**
   * 分公司编码
   */
  private String companyCode;

  /**
   * 分公司名称
   */
  private String companyName;

  /**
   * 是否起效：
   * - 1：起效
   * - -1：未起效
   */
  private Long isValid;

  /**
   * 平台创建日
   */
  private LocalDate createdDay;

  /**
   * 平台创建时间
   */
  private LocalDateTime created;

  /**
   * 平台更新时间
   */
  private LocalDateTime updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

  /**
   * 扩展信息
   */
  private ExtendData extendData;

  public DeliveryOrderExt getDeliveryOrderExt(){
    return ExtendDataTypeEnum.parseExtendData(this.getExtendData());
  }
}
