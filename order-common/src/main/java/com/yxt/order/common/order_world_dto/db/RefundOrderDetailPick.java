package com.yxt.order.common.order_world_dto.db;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 线下退单明细拣货信息
 */
@Data
public class RefundOrderDetailPick {

  /**
   * 主键
   */
  private Long id;

  /**
   * 线下订单退单明细拣货明细编号,内部生成
   */
  private String pickNo;

  /**
   * 退款单号
   */
  private String refundNo;

  /**
   * 退款单明细唯一号
   */
  private String refundDetailNo;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品批号
   */
  private String makeNo;

  /**
   * 数量
   */
  private BigDecimal count;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 创建时间
   */
  private LocalDateTime createdTime;

  /**
   * 更新时间
   */
  private LocalDateTime updatedTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;

  /**
   * 平台创建时间
   */
  private LocalDateTime created;

  /**
   * 平台更新时间
   */
  private LocalDateTime updated;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;
}
