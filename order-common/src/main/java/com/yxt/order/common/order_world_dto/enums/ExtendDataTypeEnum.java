package com.yxt.order.common.order_world_dto.enums;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yxt.lang.exception.YxtBizException;
import com.yxt.order.common.order_world_dto.db.AfterSaleOrderDetailExt;
import com.yxt.order.common.order_world_dto.db.DeliveryOrderExt;
import com.yxt.order.common.order_world_dto.db.ExtendData;
import com.yxt.order.common.order_world_dto.db.OrderDetailExt;
import com.yxt.order.common.order_world_dto.db.OrderExt;
import com.yxt.order.common.order_world_dto.db.PlatformAfterOrderExt;
import com.yxt.order.common.order_world_dto.db.PlatformOrderExt;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @since 2025/3/10
 */
@Getter
@AllArgsConstructor
public enum ExtendDataTypeEnum {

  DELIVERY_ORDER_EXTEND(1,"发货单扩展信息", DeliveryOrderExt.class),

  PLATFORM_AFTER_ORDER_EXTEND(10,"平台售后单扩展信息", PlatformAfterOrderExt.class),

  PLATFORM_AFTER_ORDER_DETAIL_EXTEND(11,"平台售后单明细扩展信息",null),

  PLATFORM_ORDER_EXTEND(20,"平台单扩展信息", PlatformOrderExt.class),

  PLATFORM_ORDER_DETAIL_EXTEND(21,"平台售后单明细扩展信息",null),

  ORDER_EXTEND(30,"订单扩展信息", OrderExt.class),

  ORDER_DETAIL_EXTEND(31,"订单明细扩展信息", OrderDetailExt.class),

  AFTER_ORDER_EXTEND(40,"售后单扩展信息",null),

  AFTER_ORDER_DETAIL_EXTEND(41,"售后单明细扩展信息", AfterSaleOrderDetailExt.class),
  ;

  private final Integer code;
  private final String desc;
  private final Class<?> clazz;


  //根据code获取枚举
  public static ExtendDataTypeEnum getByCode(Integer code) {
    for (ExtendDataTypeEnum value : ExtendDataTypeEnum.values()) {
      if (value.getCode().equals(code)) {
        return value;
      }
    }
    return null;
  }

  public static Object parseExtendDataForObject(ExtendData extendData) {
    if (ObjectUtil.isNull(extendData)) {
      return null;
    }
    ExtendDataTypeEnum dataType = getByCode(extendData.getDataType());
    if (dataType == null) {
      return null;
    }
    if (StrUtil.isBlank(extendData.getExtendJson())) {
      return null;
    }
    if (dataType.getClazz() == null) {
      throw new YxtBizException(dataType + "未配置对应的实体类！");
    }
    return JSON.parseObject(extendData.getExtendJson(), dataType.getClazz());
  }

  public static <T> T parseExtendDataWithClass(ExtendData extendData, Class<T> clazz) {
    Object parseExtendDataObject = parseExtendDataForObject(extendData);
    if (ObjectUtil.isNull(parseExtendDataObject)) {
      return null;
    }
    return clazz.cast(parseExtendDataObject);
  }

  @SuppressWarnings("unchecked")
  public static <T> T parseExtendData(ExtendData extendData) {
    Object parseExtendDataObject = parseExtendDataForObject(extendData);
    if (ObjectUtil.isNull(parseExtendDataObject)) {
      return null;
    }
    return (T) parseExtendDataObject;
  }
}
