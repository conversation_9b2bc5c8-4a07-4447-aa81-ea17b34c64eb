package com.yxt.order.common.order_world_dto.db;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 心云退货单明细拣货信息
 */
@Data
public class ReturnOrderDetailPick {

  /**
   * 主键
   */
  private Long id;

  /**
   * 退货单号
   */
  private String returnNo;

  /**
   * 退货单明细唯一号
   */
  private String returnDetailNo;

  /**
   * 退货单明细拣货唯一号
   */
  private String returnOrderDetailPickNo;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品批号
   */
  private String makeNo;

  /**
   * 数量
   */
  private BigDecimal count;

  /**
   * 平台创建时间
   */
  private LocalDateTime created;

  /**
   * 平台更新时间
   */
  private LocalDateTime updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}
