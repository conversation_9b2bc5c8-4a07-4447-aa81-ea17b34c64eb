package com.yxt.order.common.order_world_dto.db;

import java.time.LocalDateTime;
import lombok.Getter;
import lombok.Setter;

/**
 * 订单扩展信息
 */
@Getter
@Setter
public class OrderExt {

  /**
   * 超时未支付取消时间
   */
  private LocalDateTime payTimoutCancelTime;

  /**
   * 平台原始配送方式
   */
  private String deliveryType;

  /**
   * 是否是美团企客配 "1" 是 ; 其他  否
   */
  private String isMeiTuanQKP;

  /**
   * 用于订单新老模型同步
   * FORM_OLD_MODEL：表示是 老模型 同步到 新模型 的订单
   */
  private String syncFrom;

  /**
   * 请货单号
   */
  private String procurementNo;

  /**
   * 微商城扩展信息
   */
  private YDJOrderExt ydjOrderExt;


  @Getter
  @Setter
  public static class YDJOrderExt {

    /**
     * 推广门店编码
     */
    private String spreadStoreCode;

    /**
     * 分享人名称
     */
    private String shareName;

    /**
     * 员工编号 推广员
     */
    private String empCode;

    /**
     * 付费会员邀请码
     */
    private String invitationCode;

    /**
     * 是否为员工下单：0-否 1-是
     */
    private Integer staff;
  }

}
