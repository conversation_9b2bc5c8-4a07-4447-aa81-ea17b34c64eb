package com.yxt.order.common.base_order_dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 销售单下账明细表
 */
@Data
public class AccountOrderDetail {

  /**
   * 主键
   */
  private Long id;

  /**
   * 系统订单号
   */
  private Long orderNo;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品名称
   */
  private String goodsName;

  /**
   * 商品类型, 普通商品- NORMAL、赠品-GIFT
   */
  private String goodsType;

  /**
   * 生产批号
   */
  private String batchNo;

  /**
   * 商品数量
   */
  private Integer goodsCount;

  /**
   * 商品单价
   */
  private BigDecimal price;

  /**
   * 商品进价
   */
  private BigDecimal purchasePrice;

  /**
   * 商品金额
   */
  private BigDecimal goodsAmount;

  /**
   * 分摊金额
   */
  private BigDecimal shareAmount;

  /**
   * 下账单价
   */
  private BigDecimal billPrice;

  /**
   * 正单明细ID
   */
  private Long orderDetailId;

  /**
   * 第三方明细ID
   */
  private String thirdDetailId;

  /**
   * 可退款下账数
   */
  private Integer refundableAccCount;

  /**
   * 创建时间
   */
  private Date createdTime;

  /**
   * 更新时间
   */
  private Date updatedTime;

  /**
   * 是否删除 0-未删除 时间戳-已删除
   */
  private Long deleted;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}
