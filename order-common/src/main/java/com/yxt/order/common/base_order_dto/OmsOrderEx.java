package com.yxt.order.common.base_order_dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * <p>
 * 系统子订单异常信息表
 * </p>
 *
 * <AUTHOR> Agent
 * @since 2023-11-01
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OmsOrderEx implements Serializable {

  private static final long serialVersionUID = 1L;

  /**
   * 自增id
   */
  @JsonSerialize(using = ToStringSerializer.class)
  @ApiModelProperty("自增id")
  private Long id;

  /**
   * oms订单id
   */
  @JsonSerialize(using = ToStringSerializer.class)
  @ApiModelProperty("oms订单id")
  private Long omsOrderNo;

  /**
   * 订单状态:10待审核,30待配送,40待收货,100已完成,102已取消,101已关闭, (特别注意 5是处方单)
   */
  @ApiModelProperty("订单状态:10待审核,30待配送,40待收货,100已完成,102已取消,101已关闭, (特别注意 5是处方单)")
  private Integer orderStatus;

  /**
   * 异常类型
   * 2-药师审方失败 
   * 3-电话地址异常 
   * 4-快递解析失败  
   * 5-商品编码不存在  
   * 6-OMS缺货 
   * 7-WMS发货异常 
   * 8-平台接物流单失败 
   * 9-用户已申请退款 
   * 10-平台订单已取消
   */
  @ApiModelProperty("异常类型: 2-药师审方失败, 3-电话地址异常, 4-快递解析失败, 5-商品编码不存在, 6-OMS缺货, 7-WMS发货异常, 8-平台接物流单失败, 9-用户已申请退款, 10-平台订单已取消")
  private Integer exType;

  /**
   * 异常类型描述
   */
  @ApiModelProperty("异常类型描述")
  private String exTypeDesc;

  /**
   * 异常原因
   */
  @ApiModelProperty("异常原因")
  private String exReason;

  /**
   * 异常处理,0-未处理，1-已处理
   */
  @ApiModelProperty("异常处理,0-未处理，1-已处理")
  private Integer operateStatus;

  /**
   * 处理人
   */
  @ApiModelProperty("处理人")
  private String operator;

  /**
   * 创建时间
   */
  @ApiModelProperty("创建时间")
  private Date createTime;

  /**
   * 异常处理时间
   */
  @ApiModelProperty("异常处理时间")
  private Date operateTime;

  /**
   * 异常生成时间
   */
  @ApiModelProperty("异常生成时间")
  private Date busiTime;
}
