package com.yxt.order.common.order_world_dto.db;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 心云退货单作业员
 */
@Data
public class ReturnOrderWorker {

  /**
   * 主键
   */
  private Long id;

  /**
   * 退货单号
   */
  private String returnNo;

  /**
   * 平台编码
   */
  private String thirdPlatformCode;

  /**
   * 平台售后单号
   */
  private String thirdAfterOrderNo;

  /**
   * pos收银台编码
   */
  private String posCashierDeskNo;

  /**
   * 收银员编码
   */
  private String cashier;

  /**
   * 收银员姓名
   */
  private String cashierName;

  /**
   * 拣货员编码
   */
  private String picker;

  /**
   * 拣货员姓名
   */
  private String pickerName;

  /**
   * 班次
   */
  private String shiftId;

  /**
   * 班次日期
   */
  private LocalDateTime shiftDate;

  /**
   * 平台创建时间
   */
  private LocalDateTime created;

  /**
   * 平台更新时间
   */
  private LocalDateTime updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}
