package com.yxt.order.common.order_world_dto.db;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 心云退货单套装明细
 */
@Data
public class ReturnOrderSetDetail {

  /**
   * 主键
   */
  private Long id;

  /**
   * 内部退货单套装明细唯一码
   */
  private String returnDetailSetNo;

  /**
   * 退货单单号
   */
  private String returnNo;

  /**
   * 物流单号
   */
  private String logisticsTrackingNo;

  /**
   * 平台编码
   */
  private String thirdPlatformCode;

  /**
   * 平台售后单号
   */
  private String thirdAfterOrderNo;

  /**
   * 平台售后单明细唯一号
   */
  private String thirdAfterOrderDetailNo;

  /**
   * 订单明细ID
   */
  private String orderDetailNo;

  /**
   * 商品编码
   */
  private String erpCode;

  /**
   * 商品名称
   */
  private String erpName;

  /**
   * 商品数量
   */
  private BigDecimal commodityCount;

  /**
   * 商品原单价
   */
  private BigDecimal originalPrice;

  /**
   * 商品售价
   */
  private BigDecimal price;

  /**
   * 商品总额=price*数量
   */
  private BigDecimal totalAmount;

  /**
   * 商品行号
   */
  private String rowNo;

  /**
   * 三方平台商品编码
   */
  private String platformSkuId;

  /**
   * 第三方详情ID
   */
  private String platformDetailId;

  /**
   * 赠品类型 GIFT-赠品 NOT_GIFT - 非赠品
   */
  private String giftType;

  /**
   * 平台创建时间
   */
  private LocalDateTime created;

  /**
   * 平台更新时间
   */
  private LocalDateTime updated;

  /**
   * 创建人
   */
  private String createdBy;

  /**
   * 更新人
   */
  private String updatedBy;

  /**
   * 系统创建时间
   */
  private LocalDateTime sysCreateTime;

  /**
   * 系统更新时间
   */
  private LocalDateTime sysUpdateTime;

  /**
   * 数据版本，每次update+1
   */
  private Long version;
}
